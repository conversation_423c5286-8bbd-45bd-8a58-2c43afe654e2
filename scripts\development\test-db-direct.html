<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数据库连接测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .result {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
        border-left: 4px solid #007acc;
        background: #f8f9fa;
      }
      .error {
        border-left-color: #dc3545;
        background: #f8d7da;
      }
      .success {
        border-left-color: #28a745;
        background: #d4edda;
      }
      pre {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
      }
      button {
        background: #007acc;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #005a9e;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔍 数据库连接测试</h1>
      <p>这个页面用于测试 Supabase 数据库连接和数据查询</p>

      <button onclick="testConnection()">测试连接</button>
      <button onclick="testCategories()">测试分类查询</button>
      <button onclick="testTools()">测试工具查询</button>
      <button onclick="clearResults()">清空结果</button>

      <div id="results"></div>
    </div>

    <script type="module">
      // 配置信息
      const SUPABASE_URL = 'https://fytiwsutzgmygfxnqoft.supabase.co'
      const SUPABASE_ANON_KEY =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ5dGl3c3V0emdteWdmeG5xb2Z0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MDM1ODcsImV4cCI6MjA2NjM3OTU4N30.LM9vazR9QCZ4vLC_Q1lJmtCj3pEVqM6vpW4TKzntAQA'

      let supabase = null

      function addResult(message, type = 'result') {
        const results = document.getElementById('results')
        const div = document.createElement('div')
        div.className = `result ${type}`
        div.innerHTML = message
        results.appendChild(div)
        results.scrollTop = results.scrollHeight
      }

      function clearResults() {
        document.getElementById('results').innerHTML = ''
      }

      async function initSupabase() {
        if (supabase) return supabase

        try {
          const { createClient } = await import('https://cdn.skypack.dev/@supabase/supabase-js')
          supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
          return supabase
        } catch (error) {
          addResult(`❌ 初始化 Supabase 失败: ${error.message}`, 'error')
          throw error
        }
      }

      window.testConnection = async function () {
        addResult('🔗 开始测试连接...')

        try {
          const client = await initSupabase()
          addResult('✅ Supabase 客户端初始化成功', 'success')

          // 测试简单查询
          const { data, error } = await client.from('categories').select('id').limit(1)

          if (error) {
            addResult(`❌ 连接测试失败: ${error.message}`, 'error')
          } else {
            addResult('✅ 数据库连接成功', 'success')
          }
        } catch (error) {
          addResult(`❌ 连接测试异常: ${error.message}`, 'error')
        }
      }

      window.testCategories = async function () {
        addResult('📊 开始测试分类查询...')

        try {
          const client = await initSupabase()

          // 查询所有分类
          const { data: allCategories, error: allError } = await client
            .from('categories')
            .select('*')

          if (allError) {
            addResult(`❌ 查询所有分类失败: ${allError.message}`, 'error')
            return
          }

          addResult(`📊 所有分类数量: ${allCategories?.length || 0}`)
          if (allCategories && allCategories.length > 0) {
            addResult(`<pre>${JSON.stringify(allCategories, null, 2)}</pre>`)
          }

          // 查询活跃分类
          const { data: activeCategories, error: activeError } = await client
            .from('categories')
            .select('*')
            .eq('is_active', true)

          if (activeError) {
            addResult(`❌ 查询活跃分类失败: ${activeError.message}`, 'error')
            return
          }

          addResult(`✅ 活跃分类数量: ${activeCategories?.length || 0}`, 'success')
          if (activeCategories && activeCategories.length > 0) {
            addResult(`<pre>${JSON.stringify(activeCategories, null, 2)}</pre>`)
          }
        } catch (error) {
          addResult(`❌ 分类查询异常: ${error.message}`, 'error')
        }
      }

      window.testTools = async function () {
        addResult('🔧 开始测试工具查询...')

        try {
          const client = await initSupabase()

          // 查询所有工具
          const { data: allTools, error: allError } = await client.from('tools').select('*')

          if (allError) {
            addResult(`❌ 查询所有工具失败: ${allError.message}`, 'error')
            return
          }

          addResult(`🔧 所有工具数量: ${allTools?.length || 0}`)

          // 查询活跃工具
          const { data: activeTools, error: activeError } = await client
            .from('tools')
            .select('*, category:categories(*)')
            .eq('status', 'active')

          if (activeError) {
            addResult(`❌ 查询活跃工具失败: ${activeError.message}`, 'error')
            return
          }

          addResult(`✅ 活跃工具数量: ${activeTools?.length || 0}`, 'success')
          if (activeTools && activeTools.length > 0) {
            addResult(`<pre>${JSON.stringify(activeTools.slice(0, 3), null, 2)}</pre>`)
          }
        } catch (error) {
          addResult(`❌ 工具查询异常: ${error.message}`, 'error')
        }
      }

      // 页面加载时自动测试连接
      window.addEventListener('load', () => {
        addResult('🚀 页面加载完成，准备测试...')
      })
    </script>
  </body>
</html>
