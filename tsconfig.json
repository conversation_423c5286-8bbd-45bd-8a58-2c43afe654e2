{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "skipLibCheck": true, "esModuleInterop": true, "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@config/*": ["./config/*"], "@scripts/*": ["./scripts/*"], "@docs/*": ["./docs/*"]}}, "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "vite.config.*"], "exclude": ["src/**/__tests__/*"]}