<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试页面 - Ramusi 工具导航站</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    
    .container {
      text-align: center;
      max-width: 800px;
      padding: 40px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    h1 {
      font-size: 3rem;
      margin-bottom: 20px;
      background: linear-gradient(45deg, #fff, #f0f0f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    p {
      font-size: 1.2rem;
      margin-bottom: 30px;
      opacity: 0.9;
    }
    
    .status {
      padding: 20px;
      background: rgba(0, 255, 0, 0.2);
      border-radius: 10px;
      margin: 20px 0;
    }
    
    .error {
      background: rgba(255, 0, 0, 0.2);
    }
    
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 30px;
    }
    
    .tool-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      transition: transform 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .tool-card:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.2);
    }
    
    .tool-icon {
      font-size: 3rem;
      margin-bottom: 15px;
    }
    
    .tool-name {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    .tool-desc {
      font-size: 0.9rem;
      opacity: 0.8;
    }
    
    .btn {
      display: inline-block;
      padding: 12px 24px;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      margin: 10px;
    }
    
    .btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 Ramusi 工具导航站</h1>
    <p>让工作更高效的工具集合</p>
    
    <div id="status" class="status">
      ✅ 页面加载成功！Vue 应用正在初始化...
    </div>
    
    <div class="tools-grid">
      <div class="tool-card">
        <div class="tool-icon">💻</div>
        <div class="tool-name">GitHub</div>
        <div class="tool-desc">全球最大的代码托管平台</div>
      </div>
      
      <div class="tool-card">
        <div class="tool-icon">🎨</div>
        <div class="tool-name">Figma</div>
        <div class="tool-desc">协作式界面设计工具</div>
      </div>
      
      <div class="tool-card">
        <div class="tool-icon">🤖</div>
        <div class="tool-name">ChatGPT</div>
        <div class="tool-desc">AI对话助手</div>
      </div>
      
      <div class="tool-card">
        <div class="tool-icon">📝</div>
        <div class="tool-name">Notion</div>
        <div class="tool-desc">全能的笔记和协作工具</div>
      </div>
    </div>
    
    <div style="margin-top: 40px;">
      <a href="https://fastidious-malasada-5ea867.netlify.app" class="btn">
        🔄 访问完整版本
      </a>
      <a href="https://github.com/jiayuwee/advanced-tools-navigation" class="btn">
        📂 查看源码
      </a>
    </div>
  </div>
  
  <script>
    // 检查主站点状态
    setTimeout(() => {
      const status = document.getElementById('status');
      status.innerHTML = '🔍 正在检查主站点状态...';
      
      fetch('https://fastidious-malasada-5ea867.netlify.app')
        .then(response => {
          if (response.ok) {
            status.innerHTML = '✅ 主站点运行正常！';
            status.className = 'status';
          } else {
            status.innerHTML = '⚠️ 主站点可能有问题，状态码: ' + response.status;
            status.className = 'status error';
          }
        })
        .catch(error => {
          status.innerHTML = '❌ 无法连接到主站点: ' + error.message;
          status.className = 'status error';
        });
    }, 2000);
  </script>
</body>
</html>
