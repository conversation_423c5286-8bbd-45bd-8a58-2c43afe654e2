# Netlify 域名迁移指南

## 概述
这个脚本可以帮您自动在 Netlify 站点之间迁移自定义域名，解决"另一个项目已经使用此域名"的问题。

## 准备工作

### 1. 获取 Netlify 个人访问令牌
1. 登录 [Netlify 控制台](https://app.netlify.com)
2. 点击右上角头像 → **User settings**
3. 在左侧菜单选择 **Applications**
4. 点击 **Personal access tokens**
5. 点击 **New access token**
6. 输入描述名称（如：域名迁移工具）
7. 选择过期时间
8. 点击 **Generate token**
9. **重要**：复制并保存令牌，离开页面后无法再次查看

### 2. 确认站点信息
- **源站点**：当前使用 `ramusi.cn` 的站点
- **目标站点**：您想要迁移到的站点 ID（从截图看是 `spiffy-torrone-5454e1`）

## 使用方法

### 方法一：使用 Node.js 脚本（推荐）

1. 确保已安装 Node.js
2. 在项目目录运行：

```bash
node netlify-domain-migration.js <****************************************> ramusi.cn spiffy-torrone-5454e1
```

### 方法二：手动 API 调用

如果您更喜欢手动操作，可以使用以下 curl 命令：

#### 步骤 1：查找当前使用域名的站点
```bash
curl -H "Authorization: Bearer <****************************************>" \
     https://api.netlify.com/api/v1/sites
```

#### 步骤 2：从旧站点移除域名
```bash
curl -X PATCH \
     -H "Authorization: Bearer <****************************************>" \
     -H "Content-Type: application/json" \
     -d '{"custom_domain": null}' \
     https://api.netlify.com/api/v1/sites/<旧站点ID>
```

#### 步骤 3：添加域名到新站点
```bash
curl -X PATCH \
     -H "Authorization: Bearer <****************************************>" \
     -H "Content-Type: application/json" \
     -d '{"custom_domain": "ramusi.cn"}' \
     https://api.netlify.com/api/v1/sites/spiffy-torrone-5454e1
```

## 脚本功能

✅ **自动查找**：自动找到当前使用指定域名的站点  
✅ **安全验证**：在操作前验证源站点和目标站点  
✅ **错误处理**：提供详细的错误信息和回滚建议  
✅ **进度显示**：实时显示迁移进度  
✅ **等待机制**：在操作间添加适当延迟确保更改生效  

## 注意事项

⚠️ **DNS 传播**：域名迁移后，DNS 更改可能需要几分钟到几小时才能全球生效

⚠️ **SSL 证书**：迁移后 Netlify 会自动为新站点重新颁发 SSL 证书，这个过程可能需要几分钟

⚠️ **备份**：建议在迁移前记录当前的域名配置

## 故障排除

### 常见错误

1. **"API 错误 401"**
   - 检查访问令牌是否正确
   - 确认令牌未过期

2. **"API 错误 404"**
   - 检查站点 ID 是否正确
   - 确认您有权限访问该站点

3. **"域名不在源站点中"**
   - 检查域名拼写
   - 确认域名确实在指定的源站点中

4. **"另一个项目已经使用此域名"**
   - 这正是我们要解决的问题
   - 运行脚本会自动处理这种情况

### 手动回滚

如果迁移过程中出现问题，您可以：

1. 登录 Netlify 控制台
2. 进入相应站点的 **Domain management**
3. 手动添加或移除域名配置

## 验证迁移结果

迁移完成后：

1. 访问 `https://ramusi.cn` 确认指向新站点
2. 在 Netlify 控制台检查新站点的域名设置
3. 确认 SSL 证书状态为 "Active"

## 支持

如果遇到问题，请检查：
- Netlify 状态页面：https://www.netlifystatus.com/
- 您的网络连接
- 访问令牌权限

---

**提示**：首次使用建议先在测试域名上试用，确认脚本工作正常后再迁移重要域名。
