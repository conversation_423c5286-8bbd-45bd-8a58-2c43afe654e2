# 🚀 工具导航站

[![CI](https://github.com/jiayuwee/advanced-tools-navigation/actions/workflows/ci.yml/badge.svg)](https://github.com/jiayuwee/advanced-tools-navigation/actions/workflows/ci.yml)
[![Vue](https://img.shields.io/badge/Vue-3.4+-green.svg)](https://vuejs.org/)
[![Vite](https://img.shields.io/badge/Vite-5.0+-blue.svg)](https://vitejs.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3+-blue.svg)](https://www.typescriptlang.org/)

基于 **Vue 3 + Vite + Pinia + TypeScript** 的现代化工具导航站，采用 Fluent Design 设计语言，帮助您高效管理和访问常用资源。

🌐 **访问地址**: [https://ramusi.cn](https://ramusi.cn)

> 🧪 **部署测试** - 验证 GitHub Actions + Netlify + Supabase 自动化工作流 (测试时间: 2024-12-19 15:30)

![网站截图](public/screenshot.png)

## ✨ 核心功能

### 🎯 工具导航系统

- **智能搜索** - 增强搜索功能，支持实时建议、搜索历史、热门搜索
- **分类浏览** - 多维度分类和标签系统，精确筛选
- **工具详情** - 详细的工具信息、使用统计、相关推荐
- **收藏管理** - 个性化收藏夹，支持分类和批量操作

### 👤 完整用户系统

- **身份认证** - 邮箱注册、第三方登录（Google、GitHub等）
- **个人中心** - 用户资料、使用历史、个人统计
- **权限管理** - 基于角色的权限控制，数据安全保护
- **偏好设置** - 个性化设置和主题定制

### 🛒 产品商店

- **产品展示** - 分类展示数字产品和工具
- **购买流程** - 完整的购物车和订单管理
- **支付集成** - 支持多种支付方式
- **即时交付** - 数字产品购买后即时交付

### ⭐ 评价系统

- **星级评分** - 1-5星评分和详细评价
- **评价管理** - 评价展示、筛选、投票功能
- **统计分析** - 评分分布、评价趋势分析
- **互动功能** - 开发者回复、用户投票

### 🔔 实时通知

- **通知中心** - 统一的消息管理界面
- **实时推送** - 基于WebSocket的即时通知
- **个性化设置** - 通知类型和推送方式自定义
- **多端同步** - 跨设备通知同步

### 🎨 主题系统

- **多主题支持** - 亮色、暗色主题，自动切换
- **个性化定制** - 自定义颜色、字体、布局
- **预设主题** - 多种精美预设主题方案
- **主题分享** - 支持主题配置导入导出

### 📊 管理后台

- **内容管理** - 工具、产品、分类管理
- **用户管理** - 用户信息、权限统计
- **数据分析** - 访问统计、性能监控
- **反馈处理** - 用户反馈收集和处理

### 🚀 性能优化

- **代码分割** - 按需加载，优化首屏性能
- **图片优化** - WebP格式、懒加载、CDN加速
- **缓存策略** - 智能缓存提升用户体验
- **响应式设计** - 移动优先的自适应设计

## 🛠️ 技术栈

### 前端技术

- **前端框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **状态管理**: Pinia 2.1+
- **开发语言**: TypeScript 5.3+
- **样式框架**: Tailwind CSS 3.4+
- **图标库**: Lucide Vue Next

### 后端服务

- **数据库**: Supabase (PostgreSQL)
- **认证系统**: Supabase Auth
- **实时数据**: Supabase Realtime
- **文件存储**: Supabase Storage

### 本地管理

- **本地存储**: localStorage + IndexedDB
- **离线功能**: Service Worker (计划中)
- **数据同步**: 智能冲突检测和解决
- **状态管理**: Pinia + 本地持久化

### 开发工具

- **代码规范**: ESLint + Prettier
- **CI/CD**: GitHub Actions
- **部署平台**: Netlify
- **容器化**: Docker + Docker Compose

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- Supabase CLI (可选，用于本地开发)

### 数据库设置

#### 选项 1: 使用现有 Supabase 项目

1. 复制环境变量文件：

```bash
cp .env.example .env.local
```

2. 在 `.env.local` 中配置您的 Supabase 项目信息：

```env
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### 选项 2: 自动设置新项目

运行自动设置脚本：

```bash
# Linux/macOS
npm run supabase:setup

# Windows
npm run supabase:setup-win
```

详细设置指南请参考：[Supabase 部署指南](docs/SUPABASE_SETUP.md)

### 本地开发

```bash
# 克隆仓库
git clone https://github.com/jiayuwee/advanced-tools-navigation.git

# 进入项目目录
cd advanced-tools-navigation

# 安装依赖
npm install

# 配置环境变量（参考上面的数据库设置）
cp .env.example .env.local

# 启动开发服务器
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看效果。

### 本地 Supabase 开发（可选）

如果您想在本地运行完整的 Supabase 环境：

```bash
# 启动本地 Supabase 服务
npm run supabase:start

# 查看服务状态
npm run supabase:status

# 停止服务
npm run supabase:stop
```

### 生产构建

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📦 可用脚本

```bash
# 开发服务器
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint

# 类型检查
npm run type-check

# 格式化代码
npx prettier --write .

# Supabase 相关脚本
npm run supabase:start      # 启动本地 Supabase
npm run supabase:stop       # 停止本地 Supabase
npm run supabase:push       # 推送数据库迁移
npm run supabase:setup      # 自动设置项目（Linux/macOS）
npm run supabase:setup-win  # 自动设置项目（Windows）
```

## 🚀 部署

### 自动化部署

#### Netlify 部署（推荐）

- **生产环境**: [https://ramusi.cn](https://ramusi.cn)
- **预览环境**: 每个 PR 都会自动生成预览链接
- **触发方式**: 推送到 `main` 分支自动部署

#### Supabase 数据库部署

- **工作流**: `.github/workflows/supabase-deploy.yml`
- **触发条件**: `supabase/` 目录文件变更
- **功能**: 自动运行数据库迁移和前端构建

#### 配置要求

在使用 Supabase 自动部署前，需要配置 GitHub Secrets。详细配置指南：[GitHub Secrets 配置](docs/GITHUB_SECRETS_SETUP.md)

**注意**: GitHub Actions 可能显示 secrets 访问警告，这是正常的，不会影响工作流运行。

## 🐳 Docker 部署

### 使用 Docker Compose（推荐）

```bash
# 构建并启动
docker-compose up --build

# 后台运行
docker-compose up -d --build
```

### 使用 Docker

```bash
# 构建镜像
docker build -t advanced-tools-navigation .

# 运行容器
docker run -p 80:80 advanced-tools-navigation
```

## 🏗️ 项目结构

```text
advanced-tools-navigation/
├── src/                           # 源代码
│   ├── components/               # Vue 组件
│   │   ├── admin/               # 管理后台组件
│   │   └── StatusBar.vue        # 状态栏组件
│   ├── stores/                  # Pinia 状态管理
│   │   ├── tools.ts            # 工具数据管理
│   │   ├── auth.ts             # 用户认证
│   │   ├── categories.ts       # 分类管理
│   │   └── localManagement.ts  # 本地管理
│   ├── services/               # 服务层
│   │   ├── toolsService.ts     # 工具服务
│   │   ├── authService.ts      # 认证服务
│   │   └── localStorageService.ts # 本地存储服务
│   ├── views/                  # 页面组件
│   │   ├── admin/              # 管理页面
│   │   │   ├── LocalManagementView.vue # 本地管理
│   │   │   └── DashboardView.vue       # 仪表盘
│   │   └── HomeView.vue        # 首页
│   ├── types/                  # 类型定义
│   │   ├── database.ts         # Supabase 类型
│   │   └── index.ts           # 通用类型
│   ├── lib/                   # 工具库
│   │   └── supabase.ts        # Supabase 客户端
│   ├── utils/                 # 工具函数
│   ├── App.vue               # 主应用组件
│   └── main.ts               # 应用入口
├── docs/                     # 文档
│   ├── features/            # 功能文档
│   │   └── LOCAL_MANAGEMENT.md # 本地管理文档
│   ├── integration/         # 集成文档
│   │   └── SUPABASE_LOCAL_MANAGEMENT.md # Supabase 集成
│   └── deployment/          # 部署文档
├── supabase/               # Supabase 配置
│   ├── migrations/         # 数据库迁移
│   └── config.toml        # 配置文件
├── scripts/               # 脚本文件
│   ├── database/         # 数据库脚本
│   └── deployment/       # 部署脚本
├── public/               # 静态资源
├── .github/              # GitHub 配置
│   └── workflows/        # CI/CD 工作流
└── 配置文件...           # 各种配置文件
```

## 🔧 开发规范

### 代码风格

项目使用 ESLint + Prettier 进行代码规范控制：

- **ESLint**: Vue 3 + TypeScript 最佳实践
- **Prettier**: 统一的代码格式化风格
- **自动修复**: 支持保存时自动格式化

### Git 提交规范

```bash
# 功能开发
git commit -m "feat: 添加新工具分类"

# 问题修复
git commit -m "fix: 修复搜索功能bug"

# 文档更新
git commit -m "docs: 更新README文档"

# 代码重构
git commit -m "refactor: 重构状态管理逻辑"
```

## 🚀 CI/CD 流程

项目配置了完整的 GitHub Actions CI/CD 流程：

1. **代码检查**: ESLint 语法检查
2. **类型检查**: TypeScript 类型验证
3. **构建测试**: 生产环境构建验证
4. **自动部署**: 构建成功后自动部署

## 📱 响应式设计

- **桌面端**: 完整功能，侧边栏展开
- **平板端**: 自适应布局，侧边栏可折叠
- **移动端**: 移动优先设计，侧边栏抽屉式

## 🎨 设计系统

采用 Fluent Design 设计语言：

- **颜色系统**: 基于 CSS 变量的主题色彩
- **组件库**: 自定义 Vue 组件
- **图标系统**: Lucide 图标库
- **动画效果**: 流畅的过渡动画

## 🤝 贡献指南

欢迎贡献！请遵循以下步骤：

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发环境设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行代码检查
npm run lint

# 运行类型检查
npm run type-check
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Pinia](https://pinia.vuejs.org/) - Vue 状态管理库
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Lucide](https://lucide.dev/) - 精美的图标库

## 📞 联系我们

- **网站**: [https://ramusi.cn](https://ramusi.cn)
- **邮箱**: <EMAIL>
- **GitHub**: [@jiayuwee](https://github.com/jiayuwee)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
